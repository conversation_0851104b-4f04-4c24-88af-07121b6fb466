# This file configures caching rules for Cloudflare Pages.

# Cache framework assets (like CSS and JS with hashes in the name) for one year.
# The 'immutable' directive tells the browser it never needs to check for an update.
/_astro/*
  Cache-Control: public, max-age=31536000, immutable

# Cache other static assets like images, fonts, and icons for one day.
/assets/*
  Cache-Control: public, max-age=86400, public
/favicon.svg
  Cache-Control: public, max-age=86400, public
