/* Import fonts first - must be at the top */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap');

/* Admin Panel Layout Improvements */
.admin-section {
  width: 100vw;
  max-width: none;
  padding: 0;
  margin: 0 auto 2rem auto;
}
.admin-sidebar {
  min-width: 200px;
  max-width: 240px;
  width: 20%;
  background: var(--light-background);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}
.admin-table th {
  background: var(--border-light);
  font-weight: 600;
  border-bottom: 2px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 2;
  color: var(--text);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 0.75rem;
}
.admin-table td {
  border-bottom: 1px solid var(--border);
  padding: 0.75rem;
  font-size: 0.875rem;
}
.admin-table img {
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius);
}

/* Enhanced Product Listing Styles */
.products-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.products-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.no-results-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-results h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text);
}

.no-results p {
  margin: 0;
  font-size: 0.875rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
}

.product-card {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  position: relative;
}

.product-card:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--primary);
  transform: translateY(-4px);
}

.product-link {
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-link:hover {
  text-decoration: none;
  color: inherit;
}

.product-link:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: var(--radius-xl);
}

.product-image-container {
  position: relative;
  overflow: hidden;
}

.product-gallery {
  position: relative;
  width: 100%;
  aspect-ratio: 4/3;
  background: var(--border-light);
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover .product-image {
  transform: scale(1.08);
}

.image-count-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(4px);
}

.defects-badge {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  background: #fbbf24;
  color: #92400e;
  padding: 0.25rem;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow);
}

.product-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-title {
  font-family: 'Playfair Display', serif;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: var(--text);
  letter-spacing: -0.025em;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 0.75rem;
  font-weight: 700;
  color: var(--primary);
}

.price-currency {
  font-size: 1rem;
  margin-right: 0.125rem;
}

.price-amount {
  font-size: 1.5rem;
  letter-spacing: -0.025em;
}

.product-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.feature-badge {
  background: var(--border-light);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.more-features {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.product-meta {
  margin-bottom: 1rem;
}

.product-category {
  color: var(--muted);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-actions {
  padding: 0 1.5rem 1.5rem;
}

.btn-view-details {
  width: 100%;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: -0.01em;
}

.btn-view-details:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-view-details:active {
  transform: translateY(0);
}

/* Enhanced Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.modal-container {
  position: relative;
  background: var(--light-background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border);
  background: var(--border-light);
}

.modal-title {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--text);
  letter-spacing: -0.025em;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--border);
  color: var(--text);
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
}

.modal-product-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

.modal-product-gallery {
  position: sticky;
  top: 0;
}

.modal-image-container {
  background: var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.modal-main-image {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
}

.modal-main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modal-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: var(--shadow);
}

.modal-nav-btn:hover {
  background: var(--primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.modal-prev { left: 1rem; }
.modal-next { right: 1rem; }

.modal-thumbnails {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.thumbnail-btn {
  background: none;
  border: 2px solid transparent;
  border-radius: var(--radius);
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.thumbnail-btn.active {
  border-color: var(--primary);
}

.thumbnail-btn img {
  width: 60px;
  height: 45px;
  object-fit: cover;
  display: block;
}

.modal-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  aspect-ratio: 4/3;
  color: var(--muted);
  background: var(--border-light);
}

.modal-image-placeholder p {
  margin: 0.5rem 0 0 0;
  font-size: 0.875rem;
}

.modal-product-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modal-product-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: var(--text);
  letter-spacing: -0.01em;
}

.modal-product-title {
  font-family: 'Playfair Display', serif;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text);
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.modal-product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 1.5rem;
}

.modal-product-price .price-currency {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-right: 0.25rem;
}

.modal-product-price .price-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary);
  letter-spacing: -0.025em;
}

.modal-product-description p {
  margin: 0;
  line-height: 1.7;
  color: var(--text-secondary);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-light);
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-label {
  font-weight: 500;
  color: var(--text);
  min-width: 80px;
}

.feature-value {
  color: var(--text-secondary);
}

.modal-product-defects {
  background: #fef3c7;
  border: 1px solid #fde68a;
  border-radius: var(--radius);
  padding: 1rem;
}

.modal-product-defects h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #92400e;
  margin-bottom: 0.5rem;
}

.modal-product-defects p {
  margin: 0;
  color: #92400e;
  font-size: 0.875rem;
}

.modal-product-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
}

.btn-add-to-cart {
  flex: 1;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  letter-spacing: -0.01em;
}

.btn-add-to-cart:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-share-product {
  background: var(--border-light);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-share-product:hover {
  background: var(--border);
  color: var(--text);
}

/* Product Detail Page Styles */
.product-detail-main {
  padding-top: 0;
  min-height: auto;
}

.breadcrumb {
  background: var(--border-light);
  border-bottom: 1px solid var(--border);
  padding: 1rem 0;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 0.875rem;
}

.breadcrumb-list li:not(:last-child)::after {
  content: '›';
  margin-left: 0.5rem;
  color: var(--muted);
}

.breadcrumb-list a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-list a:hover {
  color: var(--primary);
}

.breadcrumb-list span[aria-current="page"] {
  color: var(--text);
  font-weight: 500;
}

.product-detail {
  padding: 3rem 0;
}

.product-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.product-images {
  position: sticky;
  top: 2rem;
}

.main-image-container {
  background: var(--border-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
  margin-bottom: 1rem;
  aspect-ratio: 1;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.main-image:hover {
  transform: scale(1.02);
}

.thumbnail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.75rem;
}

.thumbnail {
  background: none;
  border: 2px solid transparent;
  border-radius: var(--radius);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  aspect-ratio: 1;
}

.thumbnail.active {
  border-color: var(--primary);
}

.thumbnail:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.product-header {
  border-bottom: 1px solid var(--border);
  padding-bottom: 2rem;
}

.product-title {
  font-family: 'Playfair Display', serif;
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text);
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.product-price {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  letter-spacing: -0.025em;
}

.product-description p {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin: 0;
}

.product-features h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text);
}

.features-list {
  display: grid;
  gap: 0.75rem;
  margin: 0;
}

.feature-item {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-light);
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-item dt {
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

.feature-item dd {
  color: var(--text-secondary);
  margin: 0;
}

.product-defects {
  background: #fef3c7;
  border: 1px solid #fde68a;
  border-radius: var(--radius-lg);
  padding: 1.5rem;
}

.product-defects h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #92400e;
  margin: 0 0 0.75rem 0;
  font-size: 1.125rem;
}

.product-defects p {
  color: #92400e;
  margin: 0;
  font-size: 1rem;
}

.product-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
}

.btn-add-to-cart {
  flex: 1;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  letter-spacing: -0.01em;
}

.btn-add-to-cart:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-share {
  background: var(--border-light);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-share:hover {
  background: var(--border);
  color: var(--text);
}

.product-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-tag {
  background: var(--primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.related-products {
  background: var(--border-light);
  padding: 4rem 0;
  margin-top: 4rem;
}

.related-products h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-family: 'Playfair Display', serif;
  font-size: 2rem;
  color: var(--text);
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.related-product-card {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  box-shadow: var(--shadow);
}

.related-product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

.related-product-card img {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
}

.related-product-card h3 {
  padding: 1rem 1rem 0.5rem;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text);
}

.related-product-card .price {
  padding: 0 1rem 1rem;
  margin: 0;
  font-weight: 700;
  color: var(--primary);
  font-size: 1.125rem;
}
.product-title {
  font-family: 'Playfair Display', serif;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text);
  letter-spacing: -0.01em;
  min-height: 2.5em;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.product-price {
  font-weight: 700;
  color: var(--primary);
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  letter-spacing: -0.01em;
}
.product-desc {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  min-height: 2.5em;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.keypoints {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}
.keypoint-badge {
  background: var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius);
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--border);
}
.defects-warning {
  background: #fef3c7;
  color: #92400e;
  border-radius: var(--radius);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  border: 1px solid #fde68a;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1.5rem;
  }
  .header-flex {
    padding: 0 1.5rem;
    min-height: 72px;
  }
  .site-header .main-nav {
    gap: 1.5rem;
  }
  .site-header .main-nav a {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
  .logo-text {
    font-size: 1.5rem;
  }

  /* Product Grid Mobile */
  .products-container {
    padding: 0 1rem;
  }
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
  }
  .product-content {
    padding: 1.5rem;
  }
  .product-actions {
    padding: 0 1.5rem 1.5rem;
  }

  /* Product Detail Mobile */
  .product-detail {
    padding: 2rem 0;
  }

  .product-detail-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .product-images {
    position: static;
  }

  .product-title {
    font-size: 2rem;
  }

  .product-price {
    font-size: 1.75rem;
  }

  .product-description p {
    font-size: 1rem;
  }

  .feature-item {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }

  .product-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-share {
    order: -1;
    align-self: flex-start;
    width: auto;
  }

  .thumbnail-grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 0.5rem;
  }

  .related-products {
    padding: 3rem 0;
    margin-top: 3rem;
  }

  .related-products h2 {
    font-size: 1.75rem;
    margin-bottom: 2rem;
  }

  .related-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  /* Modal Mobile */
  .modal-container {
    margin: 0.5rem;
    max-height: 95vh;
  }
  .modal-header {
    padding: 1rem 1.5rem;
  }
  .modal-title {
    font-size: 1.25rem;
  }
  .modal-body {
    padding: 1.5rem;
  }
  .modal-product-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  .modal-product-title {
    font-size: 1.5rem;
  }
  .modal-product-price .price-amount {
    font-size: 2rem;
  }
  .modal-product-actions {
    flex-direction: column;
  }
  .btn-share-product {
    order: -1;
    align-self: flex-end;
    width: auto;
  }

  .homepage-card {
    padding: 3rem 2rem;
    margin: 2rem 0;
  }
  h1 {
    font-size: 2.5rem;
  }
  h2 {
    font-size: 2rem;
  }
  main {
    padding-top: 2rem;
  }
}

@media (max-width: 640px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .modal-overlay {
    padding: 1rem;
  }
  .modal-container {
    margin: 0;
    border-radius: var(--radius-lg);
  }
  .modal-header {
    padding: 1.5rem;
  }
  .modal-body {
    padding: 1.5rem;
  }
  .modal-thumbnails {
    padding: 1rem;
  }
  .thumbnail-btn img {
    width: 60px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .site-header .main-nav {
    gap: 1rem;
  }
  .site-header .main-nav a {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .product-title {
    font-size: 1.25rem;
  }
  .price-amount {
    font-size: 1.375rem;
  }

  .modal-nav-btn {
    width: 40px;
    height: 40px;
  }
  .modal-prev { left: 1rem; }
  .modal-next { right: 1rem; }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
  .product-card,
  .product-image,
  .btn-view-details,
  .modal-container {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }

  .modal-container {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .product-card {
    border-width: 2px;
  }

  .btn-view-details {
    border: 2px solid transparent;
  }

  .btn-view-details:focus {
    border-color: currentColor;
  }
}

/* Print styles */
@media print {
  .modal-overlay,
  .btn-view-details,
  .product-actions {
    display: none !important;
  }

  .product-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
}
:root {
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary: #1e293b;
  --secondary-light: #334155;
  --background: #f8fafc;
  --light-background: #ffffff;
  --card-bg: #ffffff;
  --text: #0f172a;
  --text-secondary: #475569;
  --muted: #64748b;
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --radius: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --container-width: 1200px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}



html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--background);
  color: var(--text);
  min-height: 100vh;
  transition: all 0.2s ease;
  line-height: 1.6;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 2rem;
}

.site-header {
  background: var(--light-background);
  color: var(--text);
  box-shadow: var(--shadow-sm);
  padding: 0;
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

.header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 72px;
  padding: 0 2rem;
}
.site-header .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text);
  gap: 0.75rem;
  transition: opacity 0.2s ease;
}
.site-header .logo:hover {
  opacity: 0.8;
}
.logo-text {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.025em;
  color: var(--text);
}
.site-header .main-nav {
  display: flex;
  gap: 2rem;
  margin-left: auto;
}
.site-header .main-nav a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  letter-spacing: -0.01em;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  position: relative;
}
.site-header .main-nav a:hover {
  color: var(--primary);
  background: var(--border-light);
}

main {
  padding-top: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 72px);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: all 0.2s ease;
}
a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

footer {
  margin-top: 4rem;
  color: var(--muted);
  font-size: 0.875rem;
  text-align: center;
  border-top: 1px solid var(--border);
  padding: 2rem 0;
  background: var(--light-background);
}

.cta-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--primary);
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
  text-decoration: none;
  letter-spacing: -0.01em;
}
.cta-btn:hover {
  background: var(--primary-dark);
  color: white;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
}
.cta-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow);
}

.homepage-card {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: 3rem 2.5rem;
  margin: 2rem 0;
  max-width: 640px;
  width: 100%;
  text-align: center;
  border: 1px solid var(--border);
}

h1, h2, h3 {
  font-family: 'Playfair Display', serif;
  color: var(--text);
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h2 {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

p {
  line-height: 1.7;
  color: var(--text-secondary);
}

/* Modern Admin Panel Styles */
.access-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.access-denied-content {
  text-align: center;
  max-width: 400px;
}

.access-denied-content svg {
  color: var(--muted);
  margin-bottom: 1rem;
}

.access-denied-content h2 {
  margin: 0 0 0.5rem 0;
  color: var(--text);
}

.access-denied-content p {
  margin: 0 0 2rem 0;
  color: var(--text-secondary);
}

.btn-back-home {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-back-home:hover {
  background: var(--primary-dark);
  text-decoration: none;
  color: white;
}

.admin-container {
  max-width: 100%;
  width: 100%;
  margin: 0;
  padding: 1rem;
  box-sizing: border-box;
}

.admin-header {
  background: var(--light-background);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  width: 100%;
  box-sizing: border-box;
}

.admin-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.admin-title h1 {
  margin: 0 0 0.25rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text);
}

.admin-title p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.admin-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn-sync {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--border-light);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-sync:hover {
  background: var(--border);
  color: var(--text);
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--border-light);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--border);
  color: var(--text);
}

.admin-nav {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: var(--border-light);
  padding: 0.5rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
}

.admin-tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  color: var(--text-secondary);
  border: none;
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
}

.admin-tab-btn:hover {
  background: var(--border);
  color: var(--text);
}

.admin-tab-btn.active {
  background: var(--light-background);
  color: var(--primary);
  box-shadow: var(--shadow-sm);
}

.admin-section {
  display: none;
}

.admin-section.active {
  display: block;
}

.admin-controls {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border);
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 1rem;
  background: var(--border-light);
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.admin-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text);
}

.search-group {
  grid-column: span 2;
}

.search-input-wrapper {
  position: relative;
}

.search-input-wrapper svg {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted);
  pointer-events: none;
}

.admin-select,
.admin-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: var(--light-background);
  color: var(--text);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.admin-input {
  padding-left: 2.5rem;
}

.admin-select:focus,
.admin-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.state-container svg {
  margin-bottom: 1rem;
  color: var(--muted);
}

.state-container h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text);
}

.state-container p {
  margin: 0 0 2rem 0;
}

/* Admin Products Table */
.admin-table-wrapper {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  overflow: hidden;
  margin-bottom: 2rem;
}

.admin-products-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.admin-products-table th {
  background: var(--border-light);
  color: var(--text);
  font-weight: 600;
  padding: 1rem 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-products-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
}

.product-row:hover {
  background: var(--border-light);
}

.product-name-container strong {
  display: block;
  font-weight: 600;
  color: var(--text);
  margin-bottom: 0.25rem;
}

.product-description-preview {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.category-badge {
  background: var(--primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-price-cell strong {
  color: var(--primary);
  font-size: 1rem;
}

.images-count {
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.status-indicators {
  display: flex;
  gap: 0.25rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.good {
  background: #10b981;
  color: white;
}

.status-badge.defects {
  background: #f59e0b;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-edit,
.btn-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-edit {
  background: var(--primary);
  color: white;
}

.btn-edit:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}

.btn-delete {
  background: #dc2626;
  color: white;
}

.btn-delete:hover {
  background: #b91c1c;
  transform: scale(1.05);
}

/* Categories Management */
.categories-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

.categories-list h3 {
  margin: 0 0 1rem 0;
  color: var(--text);
}

.categories-grid {
  display: grid;
  gap: 1rem;
}

.category-card {
  background: var(--light-background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
}

.category-card:hover {
  border-color: var(--primary);
  box-shadow: var(--shadow-sm);
}

.category-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text);
  font-size: 1rem;
}

.category-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-edit-category,
.btn-delete-category {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-edit-category {
  background: var(--primary);
  color: white;
}

.btn-edit-category:hover {
  background: var(--primary-dark);
}

.btn-delete-category {
  background: #dc2626;
  color: white;
}

.btn-delete-category:hover {
  background: #b91c1c;
}

.category-form-container {
  background: var(--light-background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
}

.category-form-container h3 {
  margin: 0 0 1rem 0;
  color: var(--text);
}

.category-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.no-categories {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: 2rem;
}

.admin-pagination {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.page-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border);
  background: var(--light-background);
  color: var(--text-secondary);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.page-btn:hover {
  background: var(--border);
  color: var(--text);
}

.page-btn.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border);
}

.form-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text);
}

.form-actions {
  display: flex;
  gap: 1rem;
}

.product-form {
  background: var(--light-background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 100%;
  overflow: hidden;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text);
}

.form-input {
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: var(--light-background);
  color: var(--text);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input[type="textarea"] {
  resize: vertical;
  min-height: 100px;
}

.form-help {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
  font-style: italic;
}

.notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  padding: 1rem 1.5rem;
  border-radius: var(--radius);
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.notification-success {
  background: #10b981;
}

.notification-error {
  background: #ef4444;
}

.notification-info {
  background: var(--primary);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Mobile Responsive for Admin Panel */
@media (max-width: 768px) {
  .admin-container {
    padding: 0.5rem;
    margin: 0;
    width: 100vw;
    max-width: 100vw;
    box-sizing: border-box;
  }

  .admin-header {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: var(--radius);
  }

  .admin-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .admin-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .admin-filters {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .search-group {
    grid-column: span 1;
  }

  .admin-table-wrapper {
    overflow-x: auto;
    margin: 0 -0.5rem;
    padding: 0 0.5rem;
  }

  .admin-products-table {
    min-width: 600px;
  }

  .admin-products-table th,
  .admin-products-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.75rem;
  }

  .categories-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
  }

  .form-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .product-form {
    padding: 1rem;
    margin: 0;
  }

  .admin-section {
    padding: 0;
    margin: 0;
  }

  .notification {
    top: 1rem;
    right: 0.5rem;
    left: 0.5rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .admin-container {
    padding: 0.25rem;
  }

  .admin-header {
    padding: 0.75rem;
  }

  .admin-title h1 {
    font-size: 1.5rem;
  }

  .admin-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    justify-content: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }
}
