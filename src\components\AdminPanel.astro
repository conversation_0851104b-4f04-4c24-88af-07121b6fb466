---
// Modern AdminPanel component optimized for Astro/Cloudflare Pages
import { getAllProducts } from '../lib/products';

// Load products at build time for initial display
const products = await getAllProducts();
---

<!-- Admin Access Check -->
<div id="not-admin" style="display: none;">
  <div class="access-denied">
    <h2>Access Denied</h2>
    <p>This area is restricted to administrators only.</p>
    <a href="/" class="btn-back-home">← Back to Home</a>
  </div>
</div>

<div id="admin-content" class="admin-container" style="display: none;">
  <div class="admin-header">
    <div class="admin-tabs">
      <button id="tab-list" class="admin-tab-btn active">Product List</button>
      <button id="tab-add" class="admin-tab-btn">Add/Edit Product</button>
    </div>
  </div>
<div style="display:flex;align-items:flex-start;gap:2.5rem;max-width:var(--container-width);margin:0 auto;">
  <aside class="admin-sidebar" style="min-width:180px;max-width:220px;width:18%;background:#fff8f0;border-radius:1.1rem;padding:1.2rem 1rem;margin-top:0.5rem;box-shadow:0 1.5px 8px rgba(62,39,35,0.07);display:none;">
    <div style="color:var(--muted);font-size:0.98rem;">Sidebar (coming soon)</div>
  </aside>
  <div style="flex:1;min-width:0;">
    <div id="section-list">
      <div class="admin-controls-sticky">
        <div class="admin-filter-group">
          <label for="admin-category-filter" class="admin-filter-label">Category:</label>
          <select id="admin-category-filter" class="admin-filter-select">
            <option value="">All Categories</option>
            <option value="Clothing">Clothing</option>
            <option value="Books">Books</option>
            <option value="Arts & Crafts">Arts & Crafts</option>
            <option value="Home Decor">Home Decor</option>
            <option value="Electronics">Electronics</option>
            <option value="Toys & Games">Toys & Games</option>
          </select>
        </div>
        <div class="admin-search-group">
          <input id="admin-product-search" type="search" placeholder="Search products..." class="admin-search-input" />
        </div>
      </div>
      <div class="admin-table-container">
        <table id="products-table" class="admin-table">
          <thead>
            <tr>
              <th style="width:13%;">Name</th>
              <th style="width:10%;">Images</th>
              <th style="width:22%;">Description</th>
              <th style="width:18%;">Key Points</th>
              <th style="width:13%;">Defects</th>
              <th style="width:8%;">Price</th>
              <th style="width:16%;">Actions</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
      <div id="admin-pagination" class="admin-pagination"></div>
    </div>
    <div id="section-form" style="display:none;">
      <div class="admin-form-card" style="background:#fff8f0;border-radius:1.1rem;box-shadow:0 2px 12px rgba(62,39,35,0.08);padding:2.2rem 2.8rem 1.8rem 2.8rem;max-width:900px;margin:0 auto 2.5rem auto;">
        <div style="font-size:1.25rem;font-weight:600;color:#4b3a1e;margin-bottom:0.7rem;letter-spacing:0.01em;display:flex;align-items:center;gap:0.7rem;">
          <span id="form-title">Add Product</span>
          <span style="font-size:0.98rem;color:#b8860b;font-weight:400;">(Fields marked <span style='color:#a00'>*</span> are required)</span>
        </div>
        <div style="height:1px;background:#e2cfa2;margin-bottom:1.3rem;"></div>
        <form id="product-form" style="display:flex;flex-direction:column;gap:1.2rem;">
          <input type="hidden" id="product-id" />
          <div class="admin-form-flex" style="display:flex;flex-direction:column;gap:2.2rem;">
            <div class="admin-form-col" style="flex:1;min-width:0;">
              <fieldset style="border:none;padding:0;margin:0 0 1.1rem 0;">
                <legend style="font-weight:600;font-size:1.08rem;margin-bottom:0.5rem;color:#7a5c2e;">Basic Info</legend>
                <div style="margin-bottom:0.7rem;display:flex;flex-direction:column;gap:0.5rem;">
                  <label style="font-weight:500;">Name <span style="color:#a00">*</span><input type="text" id="product-name" required style="width:100%;margin-top:0.2rem;padding:0.45rem 0.9rem;border-radius:0.7rem;border:1px solid #e2cfa2;font-size:1rem;"></label>
                  <label style="font-weight:500;">Price <span style="color:#a00">*</span><input type="number" id="product-price" min="0" step="0.01" required style="width:100%;margin-top:0.2rem;padding:0.45rem 0.9rem;border-radius:0.7rem;border:1px solid #e2cfa2;font-size:1rem;"></label>
                  <label style="font-weight:500;">Category <span style="color:#a00">*</span>
                    <select id="product-category" required style="width:100%;margin-top:0.2rem;padding:0.45rem 0.9rem;border-radius:0.7rem;border:1px solid #e2cfa2;font-size:1rem;">
                      <option value="">Select a category</option>
                      <option value="Clothing">Clothing</option>
                      <option value="Books">Books</option>
                      <option value="Arts & Crafts">Arts & Crafts</option>
                      <option value="Home Decor">Home Decor</option>
                      <option value="Electronics">Electronics</option>
                      <option value="Toys & Games">Toys & Games</option>
                    </select>
                  </label>
                  <label style="font-weight:500;">Description <span style="color:#a00">*</span><textarea id="product-desc" required style="width:100%;margin-top:0.2rem;padding:0.45rem 0.9rem;border-radius:0.7rem;border:1px solid #e2cfa2;font-size:1rem;" rows="3"></textarea></label>
                </div>
              </fieldset>
              <div style="height:1px;background:#e2cfa2;margin:0.5rem 0 1.1rem 0;"></div>
              <fieldset style="border:none;padding:0;margin:0 0 1.1rem 0;">
                <legend style="font-weight:600;font-size:1.08rem;margin-bottom:0.5rem;color:#7a5c2e;">Images</legend>
                <div style="margin-bottom:0.7rem;display:flex;align-items:center;gap:0.7rem;flex-wrap:wrap;">
                  <div id="images-list" style="margin-bottom:0.5rem;"></div>
                  <input type="url" id="new-image-url" placeholder="Add image URL" style="width:60%;padding:0.38rem 0.9rem;border-radius:0.7rem;border:1px solid #e2cfa2;font-size:1rem;">
                  <button type="button" id="add-image-btn" style="padding:0.38rem 1.1rem;border-radius:0.7rem;border:1px solid #e2cfa2;background:#f3e5c4;color:#7a5c2e;font-weight:500;font-size:1rem;">Add</button>
                </div>
              </fieldset>
            </div>
            <div class="admin-form-col" style="flex:1;min-width:0;">
              <fieldset style="border:none;padding:0;margin:0 0 1.1rem 0;">
                <legend style="font-weight:600;font-size:1.08rem;margin-bottom:0.5rem;color:#7a5c2e;">Key Points</legend>
                <div id="keypoints-list" style="margin-bottom:0.5rem;"></div>
                <div style="display:flex;gap:0.7rem;flex-wrap:wrap;align-items:center;">
                  <input type="text" id="new-key-label" placeholder="Label (e.g. Brand)" style="width:38%;padding:0.38rem 0.9rem;border-radius:0.7rem;border:1px solid #e2cfa2;font-size:1rem;">
                  <input type="text" id="new-key-value" placeholder="Value" style="width:38%;padding:0.38rem 0.9rem;border-radius:0.7rem;border:1px solid #e2cfa2;font-size:1rem;">
                  <button type="button" id="add-keypoint-btn" style="padding:0.38rem 1.1rem;border-radius:0.7rem;border:1px solid #e2cfa2;background:#f3e5c4;color:#7a5c2e;font-weight:500;font-size:1rem;">Add</button>
                </div>
              </fieldset>
              <div style="height:1px;background:#e2cfa2;margin:0.5rem 0 1.1rem 0;"></div>
              <fieldset style="border:none;padding:0;margin:0 0 1.1rem 0;">
                <legend style="font-weight:600;font-size:1.08rem;margin-bottom:0.5rem;color:#7a5c2e;">Defects</legend>
                <div id="defects-list" style="margin-bottom:0.5rem;"></div>
                <div style="display:flex;gap:0.7rem;flex-wrap:wrap;align-items:center;">
                  <input type="text" id="new-defect" placeholder="Describe defect (e.g. Small stain)" style="width:78%;padding:0.38rem 0.9rem;border-radius:0.7rem;border:1px solid #e2cfa2;font-size:1rem;">
                  <button type="button" id="add-defect-btn" style="padding:0.38rem 1.1rem;border-radius:0.7rem;border:1px solid #e2cfa2;background:#ffe0e0;color:#7a5c2e;font-weight:500;font-size:1rem;">Add</button>
                </div>
              </fieldset>
            </div>
          </div>
          <div style="display:flex;gap:0.7rem;justify-content:flex-end;margin-top:0.7rem;">
            <button type="submit" class="cta-btn" style="background:#b8860b;color:#fff;border:none;padding:0.48rem 1.5rem;font-size:1.08rem;border-radius:0.7rem;font-weight:600;">Save</button>
            <button type="button" id="cancel-edit" style="background:#fff;color:#b8860b;border:1.5px solid #b8860b;padding:0.48rem 1.5rem;font-size:1.08rem;border-radius:0.7rem;font-weight:600;display:none;">Cancel</button>
          </div>
        </form>
        <style>
          @media (min-width: 700px) {
            .admin-form-flex {
              flex-direction: row !important;
              gap: 2.8rem !important;
            }
            .admin-form-col {
              min-width: 0;
            }
          }
        </style>
      </div>
    </div>
  </div>
</div>
<div id="not-admin" style="display:none;">
  <p>You do not have access to this page.</p>
</div>
<style>
  /* Admin Header */
  .admin-header {
    background: var(--light-background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    padding: 1rem 2rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border);
  }

  .admin-tabs {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    align-items: center;
  }

  .admin-tab-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    background: var(--light-background);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    letter-spacing: -0.01em;
  }

  .admin-tab-btn.active, .admin-tab-btn:focus {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    outline: none;
    box-shadow: var(--shadow-md);
  }

  .admin-tab-btn:hover:not(.active) {
    background: var(--border-light);
    border-color: var(--primary);
  }

  /* Admin Controls */
  .admin-controls-sticky {
    position: sticky;
    top: 0;
    z-index: 10;
    background: var(--border-light);
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    border: 1px solid var(--border);
    border-bottom: none;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
  }

  .admin-filter-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .admin-search-group {
    flex: 1;
    min-width: 250px;
  }

  .admin-filter-label {
    font-weight: 500;
    color: var(--text);
    font-size: 0.875rem;
    white-space: nowrap;
  }

  .admin-filter-select, .admin-search-input {
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    background: var(--light-background);
    color: var(--text);
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .admin-filter-select:focus, .admin-search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .admin-search-input {
    width: 100%;
  }

  .admin-search-input::placeholder {
    color: var(--muted);
  }

  /* Admin Table */
  .admin-table-container {
    overflow-x: auto;
    background: var(--light-background);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    border: 1px solid var(--border);
    border-top: none;
  }

  .admin-table {
    width: 100%;
    margin-bottom: 0;
    table-layout: fixed;
    min-width: 900px;
    border-collapse: separate;
    border-spacing: 0;
  }

  .admin-table thead th {
    background: var(--border-light);
    border-bottom: 2px solid var(--border);
    font-size: 0.75rem;
    color: var(--text);
    font-weight: 600;
    padding: 1rem 0.75rem;
    text-align: left;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }

  .admin-table tbody tr {
    transition: all 0.2s ease;
    background: var(--light-background);
  }

  .admin-table tbody tr:not(:last-child) {
    border-bottom: 1px solid var(--border);
  }

  .admin-table tbody tr:hover {
    background: var(--border-light);
  }

  .admin-table td {
    padding: 0.75rem;
    border-bottom: none;
    font-size: 0.875rem;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-secondary);
  }

  .admin-table td button {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .admin-table td button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .admin-table td button.delete-btn {
    background: #dc2626;
  }

  .admin-table td button.delete-btn:hover {
    background: #b91c1c;
  }

  .admin-table img {
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    margin-right: 0.25rem;
    width: 40px;
    height: 40px;
    object-fit: cover;
    background: var(--border-light);
  }

  /* Admin Pagination */
  .admin-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin: 1.5rem 0;
  }

  @media (max-width: 768px) {
    .admin-header {
      padding: 0.75rem 1rem;
    }

    .admin-controls-sticky {
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;
      padding: 0.75rem;
    }

    .admin-filter-group {
      justify-content: space-between;
    }

    .admin-search-group {
      min-width: auto;
    }

    .admin-tab-btn {
      padding: 0.625rem 1.25rem;
      font-size: 0.8rem;
    }
  }
</style>
<script>
// Admin protection
const isAdmin = new URLSearchParams(window.location.search).get('admin') === '1';
document.getElementById('admin-content').style.display = isAdmin ? '' : 'none';
document.getElementById('not-admin').style.display = isAdmin ? 'none' : '';

// Tab logic
const tabList = document.getElementById('tab-list');
const tabAdd = document.getElementById('tab-add');
const sectionList = document.getElementById('section-list');
const sectionForm = document.getElementById('section-form');
let isEditing = false;
tabList.onclick = function() {
  sectionList.style.display = '';
  sectionForm.style.display = 'none';
  tabList.classList.add('active');
  tabAdd.classList.remove('active');
};
tabAdd.onclick = function() {
  sectionList.style.display = 'none';
  sectionForm.style.display = '';
  tabAdd.classList.add('active');
  tabList.classList.remove('active');
  if (!isEditing) {
    document.getElementById('product-form').reset();
    images = [];
    keyPoints = [];
    defects = [];
    renderImagesList();
    renderKeyPointsList();
    renderDefectsList();
    document.getElementById('form-title').textContent = 'Add Product';
    document.getElementById('cancel-edit').style.display = 'none';
  }
  isEditing = false;
};
// Default to list view
tabList.classList.add('active');
sectionList.style.display = '';
sectionForm.style.display = 'none';

// Product management logic
const LS_KEY = 'cheers_products';
let products = [];

// Helper state for form fields
let images = [];
let keyPoints = [];
let defects = [];

function loadProducts() {
  const ls = localStorage.getItem(LS_KEY);
  if (ls) {
    products = JSON.parse(ls);
    renderProducts();
  } else {
    fetch('/src/data/products.json')
      .then(r => r.json())
      .then(data => { products = data || []; renderProducts(); })
      .catch(() => { products = []; renderProducts(); });
  }
}

function saveProducts() {
  localStorage.setItem(LS_KEY, JSON.stringify(products));
}

// Admin product list state
let adminCurrentCategory = '';
let adminCurrentSearch = '';
let adminCurrentPage = 1;
const ADMIN_PAGE_SIZE = 20;

function renderProducts() {
  let filtered = products;
  if (adminCurrentCategory) {
    filtered = filtered.filter(p => p.category === adminCurrentCategory);
  }
  if (adminCurrentSearch) {
    const q = adminCurrentSearch.toLowerCase();
    filtered = filtered.filter(p =>
      (p.name && p.name.toLowerCase().includes(q)) ||
      (p.description && p.description.toLowerCase().includes(q))
    );
  }
  const total = filtered.length;
  const totalPages = Math.max(1, Math.ceil(total / ADMIN_PAGE_SIZE));
  if (adminCurrentPage > totalPages) adminCurrentPage = totalPages;
  const start = (adminCurrentPage - 1) * ADMIN_PAGE_SIZE;
  const end = start + ADMIN_PAGE_SIZE;
  const pageProducts = filtered.slice(start, end);

  const tbody = document.querySelector('#products-table tbody');
  tbody.innerHTML = '';
  if (!pageProducts.length) {
    tbody.innerHTML = '<tr><td colspan="7" style="text-align:center;color:var(--muted)">No products yet.</td></tr>';
    document.getElementById('admin-pagination').innerHTML = '';
    return;
  }
  pageProducts.forEach((p, i) => {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:120px;">${p.name}</td>
      <td>${(p.images||[]).slice(0,2).map(img => `<img src="${img}" alt="" style="width:32px;height:32px;object-fit:cover;border-radius:6px;margin-right:2px;">`).join('')}${(p.images||[]).length>2?`<span style='color:#aaa;font-size:0.9em;'>+${p.images.length-2}</span>`:''}</td>
      <td style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:180px;">${p.description ? p.description.substring(0, 60) + (p.description.length > 60 ? '…' : '') : ''}</td>
      <td style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:140px;">${(p.keyPoints||[]).map(kp => `<div><b>${kp.label}:</b> ${kp.value}</div>`).join('')}</td>
      <td style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100px;">${Array.isArray(p.defects) ? p.defects.map(def => `<div>${def}</div>`).join('') : (p.defects||'')}</td>
      <td>$${Number(p.price).toFixed(2)}</td>
      <td>
        <button onclick="editProduct(${start + i})">Edit</button>
        <button onclick="deleteProduct(${start + i})" style="margin-left:0.5rem;">Delete</button>
      </td>
    `;
    tbody.appendChild(tr);
  });

  // Pagination controls
  const pag = document.getElementById('admin-pagination');
  pag.innerHTML = '';
  if (totalPages > 1) {
    const prev = document.createElement('button');
    prev.textContent = 'Prev';
    prev.disabled = adminCurrentPage === 1;
    prev.onclick = function() { adminCurrentPage--; renderProducts(); };
    pag.appendChild(prev);
    const info = document.createElement('span');
    info.textContent = ` Page ${adminCurrentPage} of ${totalPages} `;
    pag.appendChild(info);
    const next = document.createElement('button');
    next.textContent = 'Next';
    next.disabled = adminCurrentPage === totalPages;
    next.onclick = function() { adminCurrentPage++; renderProducts(); };
    pag.appendChild(next);
  }
}
// Admin search and filter events
document.getElementById('admin-category-filter').addEventListener('change', function() {
  adminCurrentCategory = this.value;
  adminCurrentPage = 1;
  renderProducts();
});
document.getElementById('admin-product-search').addEventListener('input', function() {
  adminCurrentSearch = this.value;
  adminCurrentPage = 1;
  renderProducts();
});

function renderImagesList() {
  const list = document.getElementById('images-list');
  list.innerHTML = images.map((url, idx) => `<span style="display:inline-block;margin:2px;position:relative;">
    <img src="${url}" style="width:32px;height:32px;object-fit:cover;border-radius:6px;vertical-align:middle;">
    <button type="button" onclick="removeImage(${idx})" style="position:absolute;top:-6px;right:-6px;background:#fff;border-radius:50%;border:1px solid #ccc;font-size:0.8em;">×</button>
  </span>`).join('');
}
window.removeImage = function(idx) {
  images.splice(idx, 1);
  renderImagesList();
}

function renderKeyPointsList() {
  const list = document.getElementById('keypoints-list');
  list.innerHTML = keyPoints.map((kp, idx) => `<span style="display:inline-block;margin:2px 4px 2px 0;padding:2px 6px;background:#f3e5c4;border-radius:8px;">
    <b>${kp.label}:</b> ${kp.value} <button type="button" onclick="removeKeyPoint(${idx})" style="background:none;border:none;color:#a00;font-size:0.9em;">×</button>
  </span>`).join('');
}
window.removeKeyPoint = function(idx) {
  keyPoints.splice(idx, 1);
  renderKeyPointsList();
}
function renderDefectsList() {
  const list = document.getElementById('defects-list');
  list.innerHTML = defects.map((def, idx) => `<span style="display:inline-block;margin:2px 4px 2px 0;padding:2px 6px;background:#ffe0e0;border-radius:8px;">
    ${def} <button type="button" onclick="removeDefect(${idx})" style="background:none;border:none;color:#a00;font-size:0.9em;">×</button>
  </span>`).join('');
}
window.removeDefect = function(idx) {
  defects.splice(idx, 1);
  renderDefectsList();
}
document.getElementById('add-image-btn').onclick = function() {
  const url = document.getElementById('new-image-url').value.trim();
  if (url) {
    images.push(url);
    document.getElementById('new-image-url').value = '';
    renderImagesList();
  }
};
document.getElementById('add-keypoint-btn').onclick = function() {
  const label = document.getElementById('new-key-label').value.trim();
  const value = document.getElementById('new-key-value').value.trim();
  if (label && value) {
    keyPoints.push({ label, value });
    document.getElementById('new-key-label').value = '';
    document.getElementById('new-key-value').value = '';
    renderKeyPointsList();
  }
};
document.getElementById('add-defect-btn').onclick = function() {
  const val = document.getElementById('new-defect').value.trim();
  if (val) {
    defects.push(val);
    document.getElementById('new-defect').value = '';
    renderDefectsList();
  }
};
window.editProduct = function(idx) {
  isEditing = true;
  tabAdd.click();
  const p = products[idx];
  document.getElementById('product-id').value = idx;
  document.getElementById('product-name').value = p.name;
  document.getElementById('product-desc').value = p.description;
  document.getElementById('product-price').value = p.price;
  document.getElementById('product-category').value = p.category || '';
  images = Array.isArray(p.images) ? [...p.images] : [];
  keyPoints = Array.isArray(p.keyPoints) ? [...p.keyPoints] : [];
  defects = Array.isArray(p.defects) ? [...p.defects] : (p.defects ? [p.defects] : []);
  renderImagesList();
  renderKeyPointsList();
  renderDefectsList();
  document.getElementById('form-title').textContent = 'Edit Product';
  document.getElementById('cancel-edit').style.display = '';
}

window.deleteProduct = function(idx) {
  if (confirm('Delete this product?')) {
    products.splice(idx, 1);
    saveProducts();
    renderProducts();
    document.getElementById('product-form').reset();
    images = [];
    keyPoints = [];
    defects = [];
    renderImagesList();
    renderKeyPointsList();
    renderDefectsList();
    document.getElementById('form-title').textContent = 'Add Product';
    document.getElementById('cancel-edit').style.display = 'none';
  }
}

document.getElementById('product-form').onsubmit = function(e) {
  e.preventDefault();
  const idx = document.getElementById('product-id').value;
  const newProduct = {
    name: document.getElementById('product-name').value,
    description: document.getElementById('product-desc').value,
    price: document.getElementById('product-price').value,
    category: document.getElementById('product-category').value,
    images: [...images],
    keyPoints: [...keyPoints],
    defects: [...defects]
  };
  if (idx) {
    products[idx] = newProduct;
  } else {
    products.push(newProduct);
  }
  saveProducts();
  renderProducts();
  document.getElementById('product-form').reset();
  images = [];
  keyPoints = [];
  defects = [];
  renderImagesList();
  renderKeyPointsList();
  renderDefectsList();
  document.getElementById('form-title').textContent = 'Add Product';
  document.getElementById('cancel-edit').style.display = 'none';
};
loadProducts();
</script>
