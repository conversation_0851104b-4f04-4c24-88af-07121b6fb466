---
// Ultra-high-performance ProductsList optimized for Lighthouse 100% scores
import { generateSlug } from '../utils/slug.js';

const { products } = Astro.props;

// Pre-process products for maximum performance
const optimizedProducts = products.map((product, index) => {
  const slug = generateSlug(product.name); // Always generate slug from name for consistency
  const firstImage = product.images?.[0];
  const imageCount = product.images?.length || 0;

  return {
    id: product.id || index.toString(),
    name: product.name,
    slug,
    category: product.category,
    price: Number(product.price || 0),
    displayPrice: `$${Number(product.price || 0).toFixed(2)}`,
    description: product.description || '',
    shortDescription: (product.description || '').substring(0, 100),
    firstImage,
    imageCount,
    hasMultipleImages: imageCount > 1,
    hasDefects: !!(product.defects && product.defects.trim()),
    keyPoints: product.keyPoints || [],
    index
  };
});

// Generate minimal structured data for SEO (only first 12 products for performance)
const structuredData = {
  "@context": "https://schema.org",
  "@type": "ItemList",
  "numberOfItems": optimizedProducts.length,
  "itemListElement": optimizedProducts.slice(0, 12).map((product, index) => ({
    "@type": "Product",
    "position": index + 1,
    "name": product.name,
    "url": `/products/${product.slug}`,
    "offers": {
      "@type": "Offer",
      "price": product.price,
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    }
  }))
};
---

<!-- Structured Data for SEO -->
<script type="application/ld+json" set:html={JSON.stringify(structuredData)} is:inline></script>

<!-- Ultra-Performance Product Grid -->
<div class="products-container">
  <!-- No Results State -->
  <div id="no-results" class="no-results" style="display: none;">
    <div class="no-results-content">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
      </svg>
      <h3>No products found</h3>
      <p>Try adjusting your search or filter criteria.</p>
    </div>
  </div>

  <!-- High-Performance Products Grid -->
  <div id="products-list" class="products-grid">
    {optimizedProducts.map((product) => (
      <article 
        class="product-card" 
        data-category={product.category} 
        data-name={product.name} 
        data-description={product.shortDescription}
        data-price={product.price}
        data-index={product.index}
      >
        <a href={`/products/${product.slug}`} class="product-link" aria-label={`View ${product.name} - ${product.displayPrice}`}>
          <div class="product-image-container">
            {product.firstImage ? (
              <img 
                src={product.firstImage} 
                alt={product.name}
                class="product-image"
                loading="lazy"
                width="300"
                height="225"
                decoding="async"
              />
            ) : (
              <div class="product-image-placeholder" aria-label="No image available">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                  <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
                </svg>
              </div>
            )}
            
            {product.hasMultipleImages && (
              <div class="image-count-badge" aria-label={`${product.imageCount} images`}>
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                  <path d="M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z"/>
                </svg>
                {product.imageCount}
              </div>
            )}
            
            {product.hasDefects && (
              <div class="defects-badge" title="Has defects" aria-label="Product has defects">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                  <path d="M12,2L1,21H23M12,6L19.53,19H4.47M11,10V14H13V10M11,16V18H13V16"/>
                </svg>
              </div>
            )}
          </div>

          <div class="product-info">
            <div class="product-header">
              <h3 class="product-name">{product.name}</h3>
              <span class="product-category">{product.category}</span>
            </div>

            {product.shortDescription && (
              <p class="product-description">{product.shortDescription}{product.description.length > 100 ? '...' : ''}</p>
            )}

            <div class="product-footer">
              <div class="product-price">
                <span class="price-amount">{product.displayPrice}</span>
              </div>

              {product.keyPoints && product.keyPoints.length > 0 && (
                <div class="product-features">
                  {product.keyPoints.slice(0, 2).map((kp) => (
                    <span class="feature-badge" title={`${kp.label}: ${kp.value}`}>
                      {kp.value}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </a>
      </article>
    ))}
  </div>
</div>

<!-- Minimal JavaScript for filtering (performance optimized) -->
<script is:inline>
  document.addEventListener('DOMContentLoaded', () => {
    const categoryFilter = document.getElementById('category-filter');
    const searchInput = document.getElementById('product-search');
    const sortBy = document.getElementById('sort-by');
    const noResults = document.getElementById('no-results');
    const resultsCount = document.getElementById('results-count');
    
    // Debounce function for performance
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }
    
    function filterAndSortProducts() {
      const category = categoryFilter?.value || '';
      const search = searchInput?.value.toLowerCase() || '';
      const sortValue = sortBy?.value || 'name';
      
      const productCards = Array.from(document.querySelectorAll('.product-card'));
      let visibleCards = [];
      
      // Filter products
      productCards.forEach(card => {
        const cardCategory = card.dataset.category || '';
        const cardName = card.dataset.name.toLowerCase();
        const cardDesc = card.dataset.description.toLowerCase();
        
        const categoryMatch = !category || cardCategory === category;
        const searchMatch = !search || cardName.includes(search) || cardDesc.includes(search);
        
        if (categoryMatch && searchMatch) {
          visibleCards.push(card);
          card.style.display = '';
        } else {
          card.style.display = 'none';
        }
      });
      
      // Sort visible products
      visibleCards.sort((a, b) => {
        switch (sortValue) {
          case 'price-low':
            return parseFloat(a.dataset.price) - parseFloat(b.dataset.price);
          case 'price-high':
            return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
          case 'newest':
            return parseInt(b.dataset.index || '0') - parseInt(a.dataset.index || '0');
          case 'name':
          default:
            return a.dataset.name.localeCompare(b.dataset.name);
        }
      });
      
      // Reorder DOM elements efficiently
      const container = document.getElementById('products-list');
      if (container) {
        const fragment = document.createDocumentFragment();
        visibleCards.forEach(card => fragment.appendChild(card));
        container.appendChild(fragment);
      }
      
      // Update results count
      if (resultsCount) {
        resultsCount.textContent = `${visibleCards.length} product${visibleCards.length !== 1 ? 's' : ''}`;
      }
      
      // Show/hide no results message
      if (noResults) {
        noResults.style.display = visibleCards.length === 0 ? 'block' : 'none';
      }
    }
    
    // Bind events with performance optimization
    if (categoryFilter) {
      categoryFilter.addEventListener('change', filterAndSortProducts);
    }
    
    if (searchInput) {
      searchInput.addEventListener('input', debounce(filterAndSortProducts, 300));
    }
    
    if (sortBy) {
      sortBy.addEventListener('change', filterAndSortProducts);
    }
    
    // Apply URL parameters on initial load
    const url = new URL(window.location.href);
    const categoryParam = url.searchParams.get('category');
    const searchParam = url.searchParams.get('search');
    const sortParam = url.searchParams.get('sort');
    
    if (categoryParam && categoryFilter) {
      categoryFilter.value = categoryParam;
    }
    
    if (searchParam && searchInput) {
      searchInput.value = searchParam;
    }
    
    if (sortParam && sortBy) {
      sortBy.value = sortParam;
    }
    
    if (categoryParam || searchParam || sortParam) {
      filterAndSortProducts();
    }
  });
</script>

<style>
  /* Ultra-performance optimized styles */
  .products-container {
    width: 100%;
    max-width: 100%;
  }
  
  .products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    padding: 0;
    margin: 0;
  }

  @media (max-width: 1400px) {
    .products-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.25rem;
    }
  }

  @media (max-width: 1200px) {
    .products-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
    }
  }
  
  .product-card {
    background: var(--light-background);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    will-change: transform;
  }
  
  .product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  .product-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
  }
  
  .product-image-container {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    background: var(--border-light);
  }
  
  .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .product-card:hover .product-image {
    transform: scale(1.05);
  }
  
  .product-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--border-light);
    color: var(--muted);
  }
  
  .image-count-badge,
  .defects-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .defects-badge {
    background: rgba(245, 158, 11, 0.9);
    top: auto;
    bottom: 0.5rem;
  }
  
  .product-info {
    padding: 1rem;
  }
  
  .product-header {
    margin-bottom: 0.5rem;
  }
  
  .product-name {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text);
    line-height: 1.3;
  }
  
  .product-category {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
  }
  
  .product-description {
    margin: 0 0 1rem 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
  }
  
  .product-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
  }
  
  .price-amount {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary);
  }
  
  .product-features {
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
  }
  
  .feature-badge {
    background: var(--border-light);
    color: var(--text-secondary);
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius);
    font-size: 0.625rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
  }
  
  .no-results-content svg {
    margin-bottom: 1rem;
    color: var(--muted);
  }
  
  .no-results h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text);
  }
  
  .no-results p {
    margin: 0;
  }
  
  /* Mobile optimizations */
  @media (max-width: 768px) {
    .products-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }

    .product-info {
      padding: 1rem;
    }

    .product-name {
      font-size: 1rem;
      line-height: 1.3;
    }

    .product-description {
      font-size: 0.875rem;
      line-height: 1.4;
    }

    .price-amount {
      font-size: 1.125rem;
    }
  }

  @media (max-width: 480px) {
    .products-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .product-info {
      padding: 1.25rem;
    }

    .product-name {
      font-size: 1.125rem;
      line-height: 1.3;
    }

    .product-description {
      font-size: 0.875rem;
      line-height: 1.4;
      margin-bottom: 1rem;
    }

    .feature-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    .price-amount {
      font-size: 1.25rem;
    }
  }
</style>
