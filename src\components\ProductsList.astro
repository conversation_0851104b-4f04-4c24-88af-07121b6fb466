---
// Ultra-high-performance ProductsList optimized for Lighthouse 100% scores
const { products } = Astro.props;

// Generate URL-friendly slug from product name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Pre-process products for maximum performance
const optimizedProducts = products.map((product, index) => {
  const slug = product.slug || generateSlug(product.name);
  const firstImage = product.images?.[0];
  const imageCount = product.images?.length || 0;

  return {
    id: product.id || index.toString(),
    name: product.name,
    slug,
    category: product.category,
    price: Number(product.price || 0),
    displayPrice: `$${Number(product.price || 0).toFixed(2)}`,
    description: product.description || '',
    shortDescription: (product.description || '').substring(0, 100),
    firstImage,
    imageCount,
    hasMultipleImages: imageCount > 1,
    hasDefects: !!(product.defects && product.defects.trim()),
    keyPoints: product.keyPoints || [],
    index
  };
});

// Generate minimal structured data for SEO
const structuredData = {
  "@context": "https://schema.org",
  "@type": "ItemList",
  "numberOfItems": optimizedProducts.length,
  "itemListElement": optimizedProducts.slice(0, 12).map((product, index) => ({
    "@type": "Product",
    "position": index + 1,
    "name": product.name,
    "url": `/products/${product.slug}`,
    "offers": {
      "@type": "Offer",
      "price": product.price,
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    }
  }))
};
---

<!-- Structured Data for SEO -->
<script type="application/ld+json" set:html={JSON.stringify(structuredData)}></script>

<!-- Optimized Product Grid for Astro/Cloudflare Pages -->
<div class="products-container">
  <!-- Loading State -->
  <div id="products-loading" class="products-loading" style="display: none;">
    <div class="loading-spinner"></div>
    <p>Loading products...</p>
  </div>

  <!-- No Results State -->
  <div id="no-results" class="no-results" style="display: none;">
    <div class="no-results-icon">🔍</div>
    <h3>No products found</h3>
    <p>Try adjusting your search or filter criteria</p>
  </div>

  <!-- Products Grid -->
  <div id="products-list" class="products-grid">
    {products.map((product, index) => {
      const productSlug = product.slug || generateSlug(product.name);
      return (
        <article
          class="product-card"
          data-category={product.category || 'uncategorized'}
          data-name={product.name || ''}
          data-description={product.description || ''}
          data-price={product.price || 0}
          data-index={index}
        >
          <a href={`/products/${productSlug}`} class="product-link" aria-label={`View ${product.name}`}>
            <div class="product-image-container">
              <div class="product-gallery">
                <img
                  src={getOptimizedImageUrl(product.images?.[0]) || 'https://placehold.co/400x300/e2e8f0/64748b?text=No+Image'}
                  alt={product.name}
                  loading="lazy"
                  decoding="async"
                  width="400"
                  height="300"
                  class="product-image"
                />
                {product.images && product.images.length > 1 && (
                  <div class="image-count-badge" aria-label={`${product.images.length} images`}>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                    </svg>
                    {product.images.length}
                  </div>
                )}
              </div>
              {product.defects && (
                <div class="defects-badge" title={product.defects}>
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                  </svg>
                </div>
              )}
            </div>

            <div class="product-content">
              <h3 class="product-title">{product.name}</h3>
              <div class="product-price">
                {formatPrice(product.price)}
              </div>
              <p class="product-description">
                {product.description ? product.description.substring(0, 80) + (product.description.length > 80 ? '…' : '') : ''}
              </p>

              {product.keyPoints && product.keyPoints.length > 0 && (
                <div class="product-features">
                  {product.keyPoints.slice(0, 2).map((kp) => (
                    <span class="feature-badge" title={`${kp.label}: ${kp.value}`}>
                      {kp.value}
                    </span>
                  ))}
                  {product.keyPoints.length > 2 && (
                    <span class="feature-badge more-features">
                      +{product.keyPoints.length - 2} more
                    </span>
                  )}
                </div>
              )}

              <div class="product-meta">
                <span class="product-category">{product.category || 'Uncategorized'}</span>
              </div>
            </div>
          </a>
        </article>
      );
    })}
  </div>
</div>

<!-- Lightweight filtering script optimized for Astro -->
<script>
  // Simple, fast filtering without complex state management
  document.addEventListener('DOMContentLoaded', () => {
    const categoryFilter = document.getElementById('category-filter') as HTMLSelectElement;
    const searchInput = document.getElementById('product-search') as HTMLInputElement;
    const noResults = document.getElementById('no-results');
    const resultsCount = document.getElementById('results-count');

    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    function filterAndSortProducts() {
      const category = categoryFilter?.value || '';
      const search = searchInput?.value.toLowerCase() || '';
      const sortBy = document.getElementById('sort-by')?.value || 'name';

      const productCards = Array.from(document.querySelectorAll('.product-card'));
      let visibleCards = [];

      // Filter products
      productCards.forEach(card => {
        const cardCategory = card.dataset.category || '';
        const cardName = card.dataset.name.toLowerCase();
        const cardDesc = card.dataset.description.toLowerCase();

        const categoryMatch = !category || cardCategory === category;
        const searchMatch = !search || cardName.includes(search) || cardDesc.includes(search);

        if (categoryMatch && searchMatch) {
          visibleCards.push(card);
          card.style.display = '';
        } else {
          card.style.display = 'none';
        }
      });

      // Sort visible products
      visibleCards.sort((a, b) => {
        switch (sortBy) {
          case 'price-low':
            return parseFloat(a.dataset.price) - parseFloat(b.dataset.price);
          case 'price-high':
            return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
          case 'newest':
            // Assuming newer products have higher indices
            return parseInt(b.dataset.index || '0') - parseInt(a.dataset.index || '0');
          case 'name':
          default:
            return a.dataset.name.localeCompare(b.dataset.name);
        }
      });

      // Reorder DOM elements
      const container = document.getElementById('products-list');
      if (container) {
        visibleCards.forEach(card => {
          container.appendChild(card);
        });
      }

      // Update results count
      if (resultsCount) {
        resultsCount.textContent = `${visibleCards.length} product${visibleCards.length !== 1 ? 's' : ''}`;
      }

      // Show/hide no results message
      if (noResults) {
        noResults.style.display = visibleCards.length === 0 ? 'block' : 'none';
      }

      // Update URL with current filters (for better UX and sharing)
      const url = new URL(window.location);
      if (category) url.searchParams.set('category', category);
      else url.searchParams.delete('category');
      if (search) url.searchParams.set('search', search);
      else url.searchParams.delete('search');
      if (sortBy !== 'name') url.searchParams.set('sort', sortBy);
      else url.searchParams.delete('sort');

      window.history.replaceState({}, '', url);
    }

    // Bind events with debouncing for performance
    if (categoryFilter) {
      categoryFilter.addEventListener('change', filterAndSortProducts);
    }

    if (searchInput) {
      searchInput.addEventListener('input', debounce(filterAndSortProducts, 300));
    }

    // Bind sort event
    const sortBy = document.getElementById('sort-by') as HTMLSelectElement;
    if (sortBy) {
      sortBy.addEventListener('change', filterAndSortProducts);
    }

    // Apply URL parameters on initial load
    const url = new URL(window.location.href);
    const categoryParam = url.searchParams.get('category');
    const searchParam = url.searchParams.get('search');
    const sortParam = url.searchParams.get('sort');

    if (categoryParam && categoryFilter) {
      categoryFilter.value = categoryParam;
    }

    if (searchParam && searchInput) {
      searchInput.value = searchParam;
    }

    if (sortParam && sortBy) {
      sortBy.value = sortParam;
    }

    if (categoryParam || searchParam || sortParam) {
      filterAndSortProducts();
    }
  });
</script>
