import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    
    // Validate the request
    if (!body.trigger) {
      return new Response(JSON.stringify({ error: 'Missing trigger parameter' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Get Cloudflare Pages build hook URL from environment variables
    const buildHookUrl = import.meta.env.CLOUDFLARE_BUILD_HOOK_URL;
    
    if (!buildHookUrl) {
      console.warn('CLOUDFLARE_BUILD_HOOK_URL not configured');
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Build hook not configured' 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Trigger the Cloudflare Pages build
    const buildResponse = await fetch(buildHookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        source: 'admin-panel',
        trigger: body.trigger,
        timestamp: body.timestamp || new Date().toISOString()
      })
    });
    
    if (buildResponse.ok) {
      console.log('Build hook triggered successfully');
      
      return new Response(JSON.stringify({ 
        success: true, 
        message: 'Build triggered successfully',
        buildId: buildResponse.headers.get('x-build-id') || 'unknown'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      console.error('Build hook failed:', buildResponse.status, buildResponse.statusText);
      
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Failed to trigger build',
        status: buildResponse.status
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
  } catch (error) {
    console.error('Build hook error:', error);
    
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Internal server error',
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// Handle GET requests for testing
export const GET: APIRoute = async () => {
  return new Response(JSON.stringify({
    message: 'Build hook endpoint is active',
    timestamp: new Date().toISOString(),
    configured: !!import.meta.env.CLOUDFLARE_BUILD_HOOK_URL
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
};
