import type { APIRoute } from 'astro';
import { getAllProducts } from '../../lib/products';

export const GET: APIRoute = async () => {
  try {
    const products = await getAllProducts();
    
    return new Response(JSON.stringify(products), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
      },
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    
    return new Response(JSON.stringify({ error: 'Failed to fetch products' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};

// For development/admin purposes - in production, you'd want proper authentication
export const POST: APIRoute = async ({ request }) => {
  try {
    const product = await request.json();
    
    // In a real application, you would:
    // 1. Validate the product data
    // 2. Save to a database
    // 3. Regenerate static files if needed
    
    // For now, we'll just return success
    // The admin panel will handle localStorage for development
    
    return new Response(JSON.stringify({ success: true, product }), {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error creating product:', error);
    
    return new Response(JSON.stringify({ error: 'Failed to create product' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};

export const PUT: APIRoute = async ({ request }) => {
  try {
    const product = await request.json();
    
    // In a real application, you would:
    // 1. Validate the product data
    // 2. Update in database
    // 3. Regenerate static files if needed
    
    return new Response(JSON.stringify({ success: true, product }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error updating product:', error);
    
    return new Response(JSON.stringify({ error: 'Failed to update product' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};

export const DELETE: APIRoute = async ({ request }) => {
  try {
    const { id } = await request.json();
    
    // In a real application, you would:
    // 1. Validate the ID
    // 2. Delete from database
    // 3. Regenerate static files if needed
    
    return new Response(JSON.stringify({ success: true, id }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    
    return new Response(JSON.stringify({ error: 'Failed to delete product' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};
