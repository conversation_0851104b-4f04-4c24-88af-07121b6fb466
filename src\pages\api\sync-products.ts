import type { APIRoute } from 'astro';
import fs from 'fs/promises';
import path from 'path';

const PRODUCTS_FILE_PATH = path.join(process.cwd(), 'src/data/products.json');

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    
    if (!body.products || !Array.isArray(body.products)) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Invalid products data' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate each product
    const validatedProducts = body.products.map((product, index) => {
      const errors = [];
      
      if (!product.name || typeof product.name !== 'string') {
        errors.push(`Product ${index}: name is required`);
      }
      
      if (!product.category || typeof product.category !== 'string') {
        errors.push(`Product ${index}: category is required`);
      }
      
      if (!product.description || typeof product.description !== 'string') {
        errors.push(`Product ${index}: description is required`);
      }
      
      if (typeof product.price !== 'number' || product.price <= 0) {
        errors.push(`Product ${index}: price must be a positive number`);
      }
      
      if (errors.length > 0) {
        throw new Error(errors.join(', '));
      }
      
      // Ensure required fields and generate slug if missing
      return {
        id: product.id || Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: product.name.trim(),
        category: product.category.trim(),
        description: product.description.trim(),
        price: Number(product.price),
        images: Array.isArray(product.images) ? product.images.filter(img => img && img.trim()) : [],
        keyPoints: Array.isArray(product.keyPoints) ? product.keyPoints : [],
        defects: product.defects || null,
        slug: product.slug || generateSlug(product.name),
        createdAt: product.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    });
    
    // Write to products.json file
    await fs.writeFile(
      PRODUCTS_FILE_PATH, 
      JSON.stringify(validatedProducts, null, 2), 
      'utf8'
    );
    
    console.log(`Successfully synced ${validatedProducts.length} products to file`);
    
    // Trigger build hook
    try {
      const buildHookUrl = process.env.CLOUDFLARE_BUILD_HOOK_URL;
      if (buildHookUrl) {
        await fetch(buildHookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            source: 'product-sync',
            trigger: 'products_updated',
            timestamp: new Date().toISOString()
          })
        });
        console.log('Build hook triggered after product sync');
      }
    } catch (buildError) {
      console.warn('Failed to trigger build hook:', buildError);
      // Don't fail the sync if build hook fails
    }
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: `Successfully synced ${validatedProducts.length} products`,
      count: validatedProducts.length
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Product sync error:', error);
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message || 'Failed to sync products'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const GET: APIRoute = async () => {
  try {
    // Read current products from file
    const fileContent = await fs.readFile(PRODUCTS_FILE_PATH, 'utf8');
    const products = JSON.parse(fileContent);
    
    return new Response(JSON.stringify({
      success: true,
      products: products,
      count: products.length,
      lastModified: (await fs.stat(PRODUCTS_FILE_PATH)).mtime
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });
    
  } catch (error) {
    console.error('Failed to read products:', error);
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Failed to read products file'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

function generateSlug(name) {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}
