---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import OptimizedProductsList from '../components/OptimizedProductsList.astro';
import { generateSlug } from '../utils/slug.js';
import '../assets/global.css';

// Import products data directly for maximum performance
import products from '../data/products.json';

// Generate categories and price range at build time
const categories = [...new Set(products.map(p => p.category))].sort();
const prices = products.map(p => Number(p.price || 0));
const priceRange = {
  min: Math.min(...prices),
  max: Math.max(...prices)
};

// Generate structured data for the products page
const structuredData = {
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": "Products - Cheers Marketplace",
  "description": "Discover unique, curated products from passionate creators around the world.",
  "url": Astro.url.href,
  "mainEntity": {
    "@type": "ItemList",
    "numberOfItems": products.length,
    "itemListElement": products.slice(0, 12).map((product, index) => ({
      "@type": "Product",
      "position": index + 1,
      "name": product.name,
      "description": product.description,
      "image": product.images?.[0],
      "url": `/products/${generateSlug(product.name)}`,
      "offers": {
        "@type": "Offer",
        "price": product.price,
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      }
    }))
  }
};
---

<Layout>
  <Fragment slot="head">
    <title>Products | Cheers Marketplace</title>
    <meta name="description" content={`Discover unique, curated products from passionate creators around the world. Browse our collection of ${products.length} handpicked items.`} />
    <meta property="og:title" content="Products | Cheers Marketplace" />
    <meta property="og:description" content="Discover unique, curated products from passionate creators around the world." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <script type="application/ld+json" set:html={JSON.stringify(structuredData)} is:inline></script>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  </Fragment>

  <Header />

  <main class="products-main">
    <!-- Compact Hero Section -->
    <section class="products-hero-section">
      <div class="container">
        <div class="products-hero">
          <h1>Our Products</h1>
          <div class="hero-stats">
            <span class="stat">{products.length} Products</span>
            <span class="stat">{categories.length} Categories</span>
            <span class="stat">${priceRange.min.toFixed(0)} - ${priceRange.max.toFixed(0)}</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Compact Filters Section -->
    <section class="filters-section">
      <div class="container">
        <div class="filters-container">
          <div class="filters-grid">
            <div class="filter-group">
              <select id="category-filter" class="filter-select">
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option value={category}>{category}</option>
                ))}
              </select>
            </div>

            <div class="filter-group">
              <select id="sort-by" class="filter-select">
                <option value="name">Name A-Z</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="newest">Newest First</option>
              </select>
            </div>

            <div class="filter-group search-group">
              <input
                id="product-search"
                type="search"
                placeholder="Search products..."
                class="search-input"
                autocomplete="off"
                spellcheck="false"
              />
            </div>

            <div class="results-info">
              <span id="results-count">{products.length} products</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
      <div class="container">
        <OptimizedProductsList products={products} />
      </div>
    </section>

  </main>

  <Footer />
</Layout>

<style>
  .products-main {
    padding-top: 0;
    min-height: auto;
  }

  /* Compact Hero Section */
  .products-hero-section {
    background: linear-gradient(135deg, var(--light-background) 0%, var(--border-light) 100%);
    border-bottom: 1px solid var(--border);
    padding: 1.5rem 0;
  }

  .products-hero {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
  }

  .products-hero h1 {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
    color: var(--text);
    letter-spacing: -0.025em;
  }

  .hero-stats {
    display: flex;
    gap: 2rem;
    align-items: center;
  }

  .stat {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: var(--light-background);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    white-space: nowrap;
  }

  /* Compact Filters Section */
  .filters-section {
    padding: 0.75rem 0;
    background: var(--border-light);
    border-bottom: 1px solid var(--border);
  }

  .filters-container {
    background: var(--light-background);
    border-radius: var(--radius);
    padding: 1rem;
    border: 1px solid var(--border);
  }

  .filters-grid {
    display: grid;
    grid-template-columns: 200px 200px 1fr auto;
    gap: 1rem;
    align-items: center;
  }

  .filter-group {
    display: flex;
  }

  .search-group {
    min-width: 250px;
  }

  .results-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
  }

  .filter-select,
  .search-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    background: var(--light-background);
    color: var(--text);
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .filter-select {
    cursor: pointer;
  }

  .filter-select:focus,
  .search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
  }

  .search-input::placeholder {
    color: var(--muted);
  }

  /* Products Section */
  .products-section {
    padding: 3rem 0;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .products-hero-section {
      padding: 2rem 0;
    }

    .products-hero {
      flex-direction: column;
      align-items: flex-start;
      gap: 1.5rem;
    }

    .products-hero h1 {
      font-size: 2.25rem;
    }

    .hero-stats {
      flex-wrap: wrap;
      gap: 1rem;
    }

    .stat {
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
    }

    .filters-section {
      padding: 1rem 0;
    }

    .filters-container {
      padding: 1.25rem;
    }

    .filters-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .search-group {
      grid-column: span 1;
    }

    .results-info {
      grid-column: span 1;
      text-align: left;
      font-size: 0.875rem;
    }

    .products-section {
      padding: 2rem 0;
    }
  }

  @media (max-width: 480px) {
    .products-hero-section {
      padding: 1.5rem 0;
    }

    .products-hero h1 {
      font-size: 2rem;
    }

    .hero-stats {
      gap: 0.75rem;
    }

    .stat {
      font-size: 0.75rem;
      padding: 0.375rem 0.75rem;
    }

    .filters-container {
      padding: 1rem;
    }

    .filters-grid {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .search-group,
    .results-info {
      grid-column: span 1;
    }

    .filter-select,
    .search-input {
      padding: 0.75rem;
      font-size: 1rem;
    }

    .products-section {
      padding: 1.5rem 0;
    }
  }
</style>
