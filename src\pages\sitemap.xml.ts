import type { APIRoute } from 'astro';
import { generateSlug } from '../utils/slug.js';
import products from '../data/products.json';

export const GET: APIRoute = async ({ site }) => {
  const baseUrl = site?.toString() || 'https://cheersmarketplace2.pages.dev';
  
  // Static pages
  const staticPages = [
    '',
    '/products',
    '/about',
    '/cart',
  ];
  
  // Dynamic product pages
  const productPages = products.map(product => `/products/${generateSlug(product.name)}`);
  
  // Combine all pages
  const allPages = [...staticPages, ...productPages];
  
  // Generate sitemap XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${baseUrl}${page === '' ? '' : page}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>${page === '' ? 'weekly' : page === '/products' ? 'daily' : page.startsWith('/products/') ? 'monthly' : 'weekly'}</changefreq>
    <priority>${page === '' ? '1.0' : page === '/products' ? '0.9' : page.startsWith('/products/') ? '0.8' : '0.7'}</priority>
  </url>`).join('\n')}
</urlset>`;

  return new Response(sitemap, {
    status: 200,
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
    },
  });
};
